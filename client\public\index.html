<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#3b82f6" />
    <meta name="description" content="Advanced Todo and Notes application with modern features" />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">
    
    <!-- Meta tags for SEO -->
    <meta property="og:title" content="Advanced Todo & Notes App" />
    <meta property="og:description" content="A modern, feature-rich Todo and Notes application built with the MERN stack" />
    <meta property="og:type" content="website" />
    <meta property="og:image" content="%PUBLIC_URL%/og-image.png" />
    
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="Advanced Todo & Notes App" />
    <meta name="twitter:description" content="A modern, feature-rich Todo and Notes application built with the MERN stack" />
    <meta name="twitter:image" content="%PUBLIC_URL%/og-image.png" />
    
    <title>Advanced Todo & Notes App</title>
    
    <!-- Prevent FOUC (Flash of Unstyled Content) -->
    <script>
      // Check for saved theme preference or default to 'system'
      const savedTheme = localStorage.getItem('theme') || 'system';
      
      if (savedTheme === 'dark' || (savedTheme === 'system' && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
        document.documentElement.classList.add('dark');
      }
    </script>
    
    <style>
      /* Loading screen styles */
      #loading-screen {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: #f9fafb;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        transition: opacity 0.3s ease-out;
      }
      
      .dark #loading-screen {
        background: #111827;
      }
      
      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 3px solid #e5e7eb;
        border-top: 3px solid #3b82f6;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      .dark .loading-spinner {
        border-color: #374151;
        border-top-color: #60a5fa;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      /* Hide loading screen when React app loads */
      .app-loaded #loading-screen {
        opacity: 0;
        pointer-events: none;
      }
    </style>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    
    <!-- Loading screen -->
    <div id="loading-screen">
      <div class="loading-spinner"></div>
    </div>
    
    <!-- React app root -->
    <div id="root"></div>
    
    <!-- Hide loading screen when React app loads -->
    <script>
      window.addEventListener('load', () => {
        setTimeout(() => {
          document.body.classList.add('app-loaded');
        }, 100);
      });
    </script>
  </body>
</html>
