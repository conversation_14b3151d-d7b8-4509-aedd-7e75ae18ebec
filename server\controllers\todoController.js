const Todo = require('../models/Todo');

// @desc    Get all todos for user
// @route   GET /api/todos
// @access  Private
const getTodos = async (req, res, next) => {
  try {
    const todos = await Todo.find({ user: req.user.id })
      .populate('category', 'name color icon')
      .sort({ createdAt: -1 });

    res.status(200).json({
      success: true,
      count: todos.length,
      data: todos
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get single todo
// @route   GET /api/todos/:id
// @access  Private
const getTodo = async (req, res, next) => {
  try {
    const todo = await Todo.findOne({
      _id: req.params.id,
      user: req.user.id
    }).populate('category', 'name color icon');

    if (!todo) {
      return res.status(404).json({
        success: false,
        error: 'Todo not found'
      });
    }

    res.status(200).json({
      success: true,
      data: todo
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Create new todo
// @route   POST /api/todos
// @access  Private
const createTodo = async (req, res, next) => {
  try {
    req.body.user = req.user.id;
    
    const todo = await Todo.create(req.body);
    
    // Emit socket event for real-time updates
    if (req.io) {
      req.io.to(`user:${req.user.id}`).emit('todo-created', todo);
    }

    res.status(201).json({
      success: true,
      data: todo
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Update todo
// @route   PUT /api/todos/:id
// @access  Private
const updateTodo = async (req, res, next) => {
  try {
    const todo = await Todo.findOneAndUpdate(
      { _id: req.params.id, user: req.user.id },
      req.body,
      { new: true, runValidators: true }
    ).populate('category', 'name color icon');

    if (!todo) {
      return res.status(404).json({
        success: false,
        error: 'Todo not found'
      });
    }

    // Emit socket event for real-time updates
    if (req.io) {
      req.io.to(`user:${req.user.id}`).emit('todo-updated', todo);
    }

    res.status(200).json({
      success: true,
      data: todo
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Delete todo
// @route   DELETE /api/todos/:id
// @access  Private
const deleteTodo = async (req, res, next) => {
  try {
    const todo = await Todo.findOneAndDelete({
      _id: req.params.id,
      user: req.user.id
    });

    if (!todo) {
      return res.status(404).json({
        success: false,
        error: 'Todo not found'
      });
    }

    // Emit socket event for real-time updates
    if (req.io) {
      req.io.to(`user:${req.user.id}`).emit('todo-deleted', { id: req.params.id });
    }

    res.status(200).json({
      success: true,
      data: {}
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Toggle todo completion
// @route   PATCH /api/todos/:id/toggle
// @access  Private
const toggleTodo = async (req, res, next) => {
  try {
    const todo = await Todo.findOne({
      _id: req.params.id,
      user: req.user.id
    });

    if (!todo) {
      return res.status(404).json({
        success: false,
        error: 'Todo not found'
      });
    }

    await todo.toggleCompletion();

    // Emit socket event for real-time updates
    if (req.io) {
      req.io.to(`user:${req.user.id}`).emit('todo-completed', todo);
    }

    res.status(200).json({
      success: true,
      data: todo
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Archive todo
// @route   PATCH /api/todos/:id/archive
// @access  Private
const archiveTodo = async (req, res, next) => {
  try {
    const todo = await Todo.findOne({
      _id: req.params.id,
      user: req.user.id
    });

    if (!todo) {
      return res.status(404).json({
        success: false,
        error: 'Todo not found'
      });
    }

    await todo.archive();

    res.status(200).json({
      success: true,
      data: todo
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Duplicate todo
// @route   POST /api/todos/:id/duplicate
// @access  Private
const duplicateTodo = async (req, res, next) => {
  try {
    const originalTodo = await Todo.findOne({
      _id: req.params.id,
      user: req.user.id
    });

    if (!originalTodo) {
      return res.status(404).json({
        success: false,
        error: 'Todo not found'
      });
    }

    const duplicatedTodo = new Todo({
      ...originalTodo.toObject(),
      _id: undefined,
      title: `${originalTodo.title} (Copy)`,
      completed: false,
      completedAt: null,
      createdAt: undefined,
      updatedAt: undefined
    });

    await duplicatedTodo.save();

    res.status(201).json({
      success: true,
      data: duplicatedTodo
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Bulk update todos
// @route   PUT /api/todos/bulk
// @access  Private
const bulkUpdateTodos = async (req, res, next) => {
  try {
    const { todoIds, updates } = req.body;

    const result = await Todo.updateMany(
      { _id: { $in: todoIds }, user: req.user.id },
      updates
    );

    res.status(200).json({
      success: true,
      data: {
        modifiedCount: result.modifiedCount
      }
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get todo stats
// @route   GET /api/todos/stats
// @access  Private
const getTodoStats = async (req, res, next) => {
  try {
    const stats = await Todo.getUserStats(req.user.id);

    res.status(200).json({
      success: true,
      data: stats
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Search todos
// @route   GET /api/todos/search
// @access  Private
const searchTodos = async (req, res, next) => {
  try {
    const { q } = req.query;
    
    const todos = await Todo.find({
      user: req.user.id,
      $text: { $search: q }
    }).populate('category', 'name color icon');

    res.status(200).json({
      success: true,
      count: todos.length,
      data: todos
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get upcoming todos
// @route   GET /api/todos/upcoming
// @access  Private
const getUpcomingTodos = async (req, res, next) => {
  try {
    const todos = await Todo.find({
      user: req.user.id,
      dueDate: { $gte: new Date(), $lte: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) },
      completed: false
    }).populate('category', 'name color icon').sort({ dueDate: 1 });

    res.status(200).json({
      success: true,
      count: todos.length,
      data: todos
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get overdue todos
// @route   GET /api/todos/overdue
// @access  Private
const getOverdueTodos = async (req, res, next) => {
  try {
    const todos = await Todo.find({
      user: req.user.id,
      dueDate: { $lt: new Date() },
      completed: false
    }).populate('category', 'name color icon').sort({ dueDate: 1 });

    res.status(200).json({
      success: true,
      count: todos.length,
      data: todos
    });
  } catch (error) {
    next(error);
  }
};

module.exports = {
  getTodos,
  getTodo,
  createTodo,
  updateTodo,
  deleteTodo,
  toggleTodo,
  archiveTodo,
  duplicateTodo,
  bulkUpdateTodos,
  getTodoStats,
  searchTodos,
  getUpcomingTodos,
  getOverdueTodos
};
