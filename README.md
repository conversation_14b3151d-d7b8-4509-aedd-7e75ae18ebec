# Advanced Todo & Notes Application

A modern, feature-rich Todo and Notes application built with the MERN stack, featuring a beautiful UI with Tailwind CSS and smooth animations with Framer Motion.

## 🚀 Features

### Core Features
- **Advanced Todo Management**: Create, edit, delete, and organize todos with categories, priorities, and due dates
- **Rich Note Taking**: Notion-like rich text editor with blocks, formatting, and media support
- **User Authentication**: Secure JWT-based authentication with login/logout functionality
- **Real-time Updates**: Live synchronization using Socket.io
- **Advanced Search**: Powerful search and filtering across todos and notes

### UI/UX Features
- **Modern Design**: Clean, responsive interface built with Tailwind CSS
- **Smooth Animations**: Beautiful transitions and micro-interactions with Framer Motion
- **Dark/Light Theme**: Toggle between dark and light modes
- **Drag & Drop**: Intuitive drag-and-drop functionality for todos
- **Mobile Responsive**: Optimized for all device sizes

### Advanced Features
- **Categories & Tags**: Organize todos and notes with custom categories and tags
- **Subtasks**: Break down complex todos into manageable subtasks
- **Due Dates & Reminders**: Set deadlines and get notifications
- **Collaborative Features**: Share and collaborate on notes and todos
- **Export/Import**: Backup and restore your data
- **Offline Support**: Work offline with automatic sync when online

## 🛠️ Tech Stack

### Frontend
- **React 18**: Modern React with hooks and functional components
- **Tailwind CSS**: Utility-first CSS framework for rapid UI development
- **Framer Motion**: Production-ready motion library for React
- **React Router**: Client-side routing
- **Zustand**: Lightweight state management
- **React Hook Form**: Performant forms with easy validation
- **React Quill**: Rich text editor for notes

### Backend
- **Node.js**: JavaScript runtime for server-side development
- **Express.js**: Fast, unopinionated web framework
- **MongoDB**: NoSQL database for flexible data storage
- **Mongoose**: Elegant MongoDB object modeling
- **Socket.io**: Real-time bidirectional event-based communication
- **JWT**: JSON Web Tokens for secure authentication

### Development Tools
- **Nodemon**: Auto-restart server during development
- **Concurrently**: Run multiple commands concurrently
- **Jest**: Testing framework
- **ESLint**: Code linting and formatting

## 📦 Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd advanced-todo-notes-app
   ```

2. **Install dependencies**
   ```bash
   npm install
   npm run install-all
   ```

3. **Environment Setup**
   Create a `.env` file in the server directory:
   ```env
   NODE_ENV=development
   PORT=5000
   MONGODB_URI=mongodb://localhost:27017/todo-notes-app
   JWT_SECRET=your-super-secret-jwt-key
   JWT_EXPIRE=30d
   ```

4. **Start the application**
   ```bash
   npm run dev
   ```

   This will start both the server (port 5000) and client (port 3000) concurrently.

## 🏗️ Project Structure

```
advanced-todo-notes-app/
├── client/                 # React frontend
│   ├── public/
│   ├── src/
│   │   ├── components/     # Reusable UI components
│   │   ├── pages/          # Page components
│   │   ├── hooks/          # Custom React hooks
│   │   ├── store/          # State management
│   │   ├── utils/          # Utility functions
│   │   ├── styles/         # Global styles
│   │   └── App.js
│   └── package.json
├── server/                 # Node.js backend
│   ├── controllers/        # Route controllers
│   ├── models/            # Database models
│   ├── routes/            # API routes
│   ├── middleware/        # Custom middleware
│   ├── utils/             # Utility functions
│   ├── config/            # Configuration files
│   └── index.js
└── package.json
```

## 🚀 Getting Started

1. Make sure you have Node.js (v16+) and MongoDB installed
2. Follow the installation steps above
3. Open your browser and navigate to `http://localhost:3000`
4. Create an account or login to start using the application

## 📱 Usage

### Creating Todos
1. Click the "Add Todo" button
2. Enter your todo title and description
3. Set priority, category, and due date (optional)
4. Save to add to your todo list

### Taking Notes
1. Navigate to the Notes section
2. Click "New Note" to create a note
3. Use the rich text editor to format your content
4. Add tags and categories for better organization

### Advanced Features
- **Drag & Drop**: Reorder todos by dragging them
- **Search**: Use the search bar to find specific todos or notes
- **Filters**: Filter by category, priority, or completion status
- **Themes**: Toggle between light and dark themes in settings

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Inspired by Notion's clean interface and powerful features
- Built with modern web technologies and best practices
- Designed for productivity and user experience
