const express = require('express');
const { body } = require('express-validator');
const { protect } = require('../middleware/auth');
const {
  getCategories,
  getCategory,
  createCategory,
  updateCategory,
  deleteCategory,
  reorderCategories
} = require('../controllers/categoryController');

const router = express.Router();

// All routes are protected
router.use(protect);

// Validation rules
const categoryValidation = [
  body('name')
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('Name must be between 1 and 50 characters'),
  body('description')
    .optional()
    .isLength({ max: 200 })
    .withMessage('Description cannot be more than 200 characters'),
  body('color')
    .optional()
    .matches(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/)
    .withMessage('Color must be a valid hex color'),
  body('icon')
    .optional()
    .isLength({ max: 50 })
    .withMessage('Icon name cannot be more than 50 characters')
];

// Routes
router.route('/')
  .get(getCategories)
  .post(categoryValidation, createCategory);

router.route('/reorder')
  .put(reorderCategories);

router.route('/:id')
  .get(getCategory)
  .put(categoryValidation, updateCategory)
  .delete(deleteCategory);

module.exports = router;
