const mongoose = require('mongoose');

const BlockSchema = new mongoose.Schema({
  id: {
    type: String,
    required: true
  },
  type: {
    type: String,
    enum: [
      'paragraph',
      'heading1',
      'heading2',
      'heading3',
      'bulletList',
      'numberedList',
      'quote',
      'code',
      'divider',
      'image',
      'video',
      'file',
      'table',
      'callout',
      'toggle',
      'bookmark'
    ],
    default: 'paragraph'
  },
  content: {
    type: mongoose.Schema.Types.Mixed,
    default: {}
  },
  properties: {
    type: mongoose.Schema.Types.Mixed,
    default: {}
  },
  children: [String], // Array of block IDs
  parent: {
    type: String,
    default: null
  },
  order: {
    type: Number,
    default: 0
  }
}, {
  timestamps: true
});

const NoteSchema = new mongoose.Schema({
  title: {
    type: String,
    required: [true, 'Please add a note title'],
    trim: true,
    maxlength: [200, 'Title cannot be more than 200 characters']
  },
  content: {
    type: String,
    default: ''
  },
  blocks: [BlockSchema],
  plainText: {
    type: String,
    default: ''
  },
  excerpt: {
    type: String,
    maxlength: [300, 'Excerpt cannot be more than 300 characters']
  },
  tags: [{
    type: String,
    trim: true,
    maxlength: [30, 'Tag cannot be more than 30 characters']
  }],
  category: {
    type: mongoose.Schema.ObjectId,
    ref: 'Category',
    default: null
  },
  user: {
    type: mongoose.Schema.ObjectId,
    ref: 'User',
    required: true
  },
  collaborators: [{
    user: {
      type: mongoose.Schema.ObjectId,
      ref: 'User'
    },
    permission: {
      type: String,
      enum: ['read', 'write', 'admin'],
      default: 'read'
    },
    addedAt: {
      type: Date,
      default: Date.now
    }
  }],
  isPublic: {
    type: Boolean,
    default: false
  },
  publicSlug: {
    type: String,
    unique: true,
    sparse: true
  },
  template: {
    isTemplate: {
      type: Boolean,
      default: false
    },
    templateName: String,
    templateDescription: String,
    templateCategory: String
  },
  attachments: [{
    filename: String,
    originalName: String,
    mimetype: String,
    size: Number,
    url: String,
    uploadedAt: {
      type: Date,
      default: Date.now
    }
  }],
  metadata: {
    wordCount: {
      type: Number,
      default: 0
    },
    readingTime: {
      type: Number,
      default: 0
    },
    lastEditedBy: {
      type: mongoose.Schema.ObjectId,
      ref: 'User'
    }
  },
  version: {
    type: Number,
    default: 1
  },
  versions: [{
    version: Number,
    content: String,
    blocks: [BlockSchema],
    editedBy: {
      type: mongoose.Schema.ObjectId,
      ref: 'User'
    },
    editedAt: {
      type: Date,
      default: Date.now
    },
    changeDescription: String
  }],
  isFavorite: {
    type: Boolean,
    default: false
  },
  isPinned: {
    type: Boolean,
    default: false
  },
  isArchived: {
    type: Boolean,
    default: false
  },
  archivedAt: {
    type: Date,
    default: null
  },
  deletedAt: {
    type: Date,
    default: null
  },
  reminderDate: {
    type: Date,
    default: null
  },
  linkedNotes: [{
    type: mongoose.Schema.ObjectId,
    ref: 'Note'
  }],
  backlinks: [{
    type: mongoose.Schema.ObjectId,
    ref: 'Note'
  }]
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for reading time calculation
NoteSchema.virtual('estimatedReadingTime').get(function() {
  const wordsPerMinute = 200;
  return Math.ceil(this.metadata.wordCount / wordsPerMinute);
});

// Indexes for better query performance
NoteSchema.index({ user: 1, createdAt: -1 });
NoteSchema.index({ user: 1, category: 1 });
NoteSchema.index({ user: 1, tags: 1 });
NoteSchema.index({ user: 1, isFavorite: 1 });
NoteSchema.index({ user: 1, isPinned: 1 });
NoteSchema.index({ publicSlug: 1 });

// Text index for search functionality
NoteSchema.index({
  title: 'text',
  content: 'text',
  plainText: 'text',
  tags: 'text'
});

// Pre-save middleware
NoteSchema.pre('save', function(next) {
  // Generate excerpt from plain text
  if (this.isModified('plainText') && this.plainText) {
    this.excerpt = this.plainText.substring(0, 300).trim();
    if (this.plainText.length > 300) {
      this.excerpt += '...';
    }
  }

  // Calculate word count and reading time
  if (this.isModified('plainText')) {
    const words = this.plainText.trim().split(/\s+/).filter(word => word.length > 0);
    this.metadata.wordCount = words.length;
    this.metadata.readingTime = Math.ceil(words.length / 200); // 200 words per minute
  }

  // Generate public slug if note is public
  if (this.isModified('isPublic') && this.isPublic && !this.publicSlug) {
    this.publicSlug = this.title.toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/^-+|-+$/g, '') + '-' + Date.now();
  }

  // Clear public slug if note is no longer public
  if (this.isModified('isPublic') && !this.isPublic) {
    this.publicSlug = undefined;
  }

  // Set archived date
  if (this.isModified('isArchived') && this.isArchived && !this.archivedAt) {
    this.archivedAt = new Date();
  }

  // Clear archived date when unarchiving
  if (this.isModified('isArchived') && !this.isArchived && this.archivedAt) {
    this.archivedAt = null;
  }

  next();
});

// Static method to get user statistics
NoteSchema.statics.getUserStats = async function(userId) {
  const stats = await this.aggregate([
    { $match: { user: mongoose.Types.ObjectId(userId), isArchived: false, deletedAt: null } },
    {
      $group: {
        _id: null,
        total: { $sum: 1 },
        favorites: { $sum: { $cond: ['$isFavorite', 1, 0] } },
        pinned: { $sum: { $cond: ['$isPinned', 1, 0] } },
        public: { $sum: { $cond: ['$isPublic', 1, 0] } },
        templates: { $sum: { $cond: ['$template.isTemplate', 1, 0] } },
        totalWords: { $sum: '$metadata.wordCount' }
      }
    }
  ]);

  return stats[0] || {
    total: 0,
    favorites: 0,
    pinned: 0,
    public: 0,
    templates: 0,
    totalWords: 0
  };
};

// Instance method to create version
NoteSchema.methods.createVersion = function(editedBy, changeDescription = '') {
  const version = {
    version: this.version,
    content: this.content,
    blocks: this.blocks,
    editedBy,
    editedAt: new Date(),
    changeDescription
  };

  this.versions.push(version);
  this.version += 1;
  this.metadata.lastEditedBy = editedBy;

  // Keep only last 10 versions
  if (this.versions.length > 10) {
    this.versions = this.versions.slice(-10);
  }

  return this.save();
};

// Instance method to archive
NoteSchema.methods.archive = function() {
  this.isArchived = true;
  this.archivedAt = new Date();
  return this.save();
};

// Instance method to soft delete
NoteSchema.methods.softDelete = function() {
  this.deletedAt = new Date();
  return this.save();
};

module.exports = mongoose.model('Note', NoteSchema);
