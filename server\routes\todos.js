const express = require('express');
const { body } = require('express-validator');
const { protect } = require('../middleware/auth');
const {
  getTodos,
  getTodo,
  createTodo,
  updateTodo,
  deleteTodo,
  toggleTodo,
  bulkUpdateTodos,
  getTodoStats,
  searchTodos,
  getUpcomingTodos,
  getOverdueTodos,
  archiveTodo,
  duplicateTodo
} = require('../controllers/todoController');

const router = express.Router();

// All routes are protected
router.use(protect);

// Validation rules
const todoValidation = [
  body('title')
    .trim()
    .isLength({ min: 1, max: 200 })
    .withMessage('Title must be between 1 and 200 characters'),
  body('description')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('Description cannot be more than 1000 characters'),
  body('priority')
    .optional()
    .isIn(['low', 'medium', 'high', 'urgent'])
    .withMessage('Priority must be low, medium, high, or urgent'),
  body('dueDate')
    .optional()
    .isISO8601()
    .withMessage('Due date must be a valid date'),
  body('tags')
    .optional()
    .isArray()
    .withMessage('Tags must be an array'),
  body('tags.*')
    .optional()
    .isLength({ max: 30 })
    .withMessage('Each tag cannot be more than 30 characters')
];

// Routes
router.route('/')
  .get(getTodos)
  .post(todoValidation, createTodo);

router.route('/stats')
  .get(getTodoStats);

router.route('/search')
  .get(searchTodos);

router.route('/upcoming')
  .get(getUpcomingTodos);

router.route('/overdue')
  .get(getOverdueTodos);

router.route('/bulk')
  .put(bulkUpdateTodos);

router.route('/:id')
  .get(getTodo)
  .put(todoValidation, updateTodo)
  .delete(deleteTodo);

router.route('/:id/toggle')
  .patch(toggleTodo);

router.route('/:id/archive')
  .patch(archiveTodo);

router.route('/:id/duplicate')
  .post(duplicateTodo);

module.exports = router;
