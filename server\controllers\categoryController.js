const Category = require('../models/Category');

// @desc    Get all categories for user
// @route   GET /api/categories
// @access  Private
const getCategories = async (req, res, next) => {
  try {
    const categories = await Category.find({ user: req.user.id })
      .populate('todoCount')
      .populate('noteCount')
      .sort({ order: 1, createdAt: 1 });

    res.status(200).json({
      success: true,
      count: categories.length,
      data: categories
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get single category
// @route   GET /api/categories/:id
// @access  Private
const getCategory = async (req, res, next) => {
  try {
    const category = await Category.findOne({
      _id: req.params.id,
      user: req.user.id
    })
      .populate('todos')
      .populate('notes');

    if (!category) {
      return res.status(404).json({
        success: false,
        error: 'Category not found'
      });
    }

    res.status(200).json({
      success: true,
      data: category
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Create new category
// @route   POST /api/categories
// @access  Private
const createCategory = async (req, res, next) => {
  try {
    req.body.user = req.user.id;
    
    const category = await Category.create(req.body);
    
    // Emit socket event for real-time updates
    if (req.io) {
      req.io.to(`user:${req.user.id}`).emit('category-created', category);
    }

    res.status(201).json({
      success: true,
      data: category
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Update category
// @route   PUT /api/categories/:id
// @access  Private
const updateCategory = async (req, res, next) => {
  try {
    const category = await Category.findOneAndUpdate(
      { _id: req.params.id, user: req.user.id },
      req.body,
      { new: true, runValidators: true }
    );

    if (!category) {
      return res.status(404).json({
        success: false,
        error: 'Category not found'
      });
    }

    // Emit socket event for real-time updates
    if (req.io) {
      req.io.to(`user:${req.user.id}`).emit('category-updated', category);
    }

    res.status(200).json({
      success: true,
      data: category
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Delete category
// @route   DELETE /api/categories/:id
// @access  Private
const deleteCategory = async (req, res, next) => {
  try {
    const category = await Category.findOne({
      _id: req.params.id,
      user: req.user.id
    });

    if (!category) {
      return res.status(404).json({
        success: false,
        error: 'Category not found'
      });
    }

    // Check if category is default
    if (category.isDefault) {
      return res.status(400).json({
        success: false,
        error: 'Cannot delete default category'
      });
    }

    // TODO: Handle todos and notes that belong to this category
    // Either move them to default category or handle as needed

    await category.remove();

    // Emit socket event for real-time updates
    if (req.io) {
      req.io.to(`user:${req.user.id}`).emit('category-deleted', { id: req.params.id });
    }

    res.status(200).json({
      success: true,
      data: {}
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Reorder categories
// @route   PUT /api/categories/reorder
// @access  Private
const reorderCategories = async (req, res, next) => {
  try {
    const { categoryOrders } = req.body; // Array of { id, order }

    const updatePromises = categoryOrders.map(({ id, order }) =>
      Category.findOneAndUpdate(
        { _id: id, user: req.user.id },
        { order },
        { new: true }
      )
    );

    const updatedCategories = await Promise.all(updatePromises);

    res.status(200).json({
      success: true,
      data: updatedCategories
    });
  } catch (error) {
    next(error);
  }
};

module.exports = {
  getCategories,
  getCategory,
  createCategory,
  updateCategory,
  deleteCategory,
  reorderCategories
};
