const mongoose = require('mongoose');

const CategorySchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Please add a category name'],
    trim: true,
    maxlength: [50, 'Category name cannot be more than 50 characters']
  },
  description: {
    type: String,
    maxlength: [200, 'Description cannot be more than 200 characters']
  },
  color: {
    type: String,
    default: '#3b82f6',
    match: [/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, 'Please provide a valid hex color']
  },
  icon: {
    type: String,
    default: 'folder'
  },
  user: {
    type: mongoose.Schema.ObjectId,
    ref: 'User',
    required: true
  },
  isDefault: {
    type: Boolean,
    default: false
  },
  order: {
    type: Number,
    default: 0
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for todos in this category
CategorySchema.virtual('todos', {
  ref: 'Todo',
  localField: '_id',
  foreignField: 'category',
  justOne: false
});

// Virtual for notes in this category
CategorySchema.virtual('notes', {
  ref: 'Note',
  localField: '_id',
  foreignField: 'category',
  justOne: false
});

// Virtual for todo count
CategorySchema.virtual('todoCount', {
  ref: 'Todo',
  localField: '_id',
  foreignField: 'category',
  count: true
});

// Virtual for note count
CategorySchema.virtual('noteCount', {
  ref: 'Note',
  localField: '_id',
  foreignField: 'category',
  count: true
});

// Ensure user can't have duplicate category names
CategorySchema.index({ name: 1, user: 1 }, { unique: true });

// Pre-save middleware to handle default categories
CategorySchema.pre('save', async function(next) {
  // If this is being set as default, unset other defaults for this user
  if (this.isDefault && this.isModified('isDefault')) {
    await this.constructor.updateMany(
      { user: this.user, _id: { $ne: this._id } },
      { isDefault: false }
    );
  }
  next();
});

// Static method to create default categories for new users
CategorySchema.statics.createDefaultCategories = async function(userId) {
  const defaultCategories = [
    {
      name: 'Personal',
      description: 'Personal tasks and notes',
      color: '#3b82f6',
      icon: 'user',
      user: userId,
      isDefault: true,
      order: 1
    },
    {
      name: 'Work',
      description: 'Work-related tasks and notes',
      color: '#10b981',
      icon: 'briefcase',
      user: userId,
      order: 2
    },
    {
      name: 'Ideas',
      description: 'Creative ideas and inspiration',
      color: '#f59e0b',
      icon: 'lightbulb',
      user: userId,
      order: 3
    },
    {
      name: 'Shopping',
      description: 'Shopping lists and reminders',
      color: '#ef4444',
      icon: 'shopping-cart',
      user: userId,
      order: 4
    }
  ];

  try {
    await this.insertMany(defaultCategories);
    console.log(`✅ Default categories created for user ${userId}`);
  } catch (error) {
    console.error('❌ Error creating default categories:', error);
  }
};

module.exports = mongoose.model('Category', CategorySchema);
