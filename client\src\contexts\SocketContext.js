import React, { createContext, useContext, useEffect, useRef, useState } from 'react';
import { io } from 'socket.io-client';
import { useAuth } from './AuthContext';
import { toast } from 'react-hot-toast';

// Create context
const SocketContext = createContext();

// Socket provider component
export const SocketProvider = ({ children }) => {
  const { user, token } = useAuth();
  const [socket, setSocket] = useState(null);
  const [isConnected, setIsConnected] = useState(false);
  const [onlineUsers, setOnlineUsers] = useState(new Set());
  const reconnectAttempts = useRef(0);
  const maxReconnectAttempts = 5;

  // Initialize socket connection
  useEffect(() => {
    if (user && token) {
      const newSocket = io(process.env.REACT_APP_SERVER_URL || 'http://localhost:5000', {
        auth: {
          token: token
        },
        transports: ['websocket', 'polling'],
        timeout: 20000,
        forceNew: true
      });

      // Connection event handlers
      newSocket.on('connect', () => {
        console.log('🔌 Connected to server');
        setIsConnected(true);
        setSocket(newSocket);
        reconnectAttempts.current = 0;
      });

      newSocket.on('disconnect', (reason) => {
        console.log('🔌 Disconnected from server:', reason);
        setIsConnected(false);
        
        if (reason === 'io server disconnect') {
          // Server disconnected, try to reconnect
          newSocket.connect();
        }
      });

      newSocket.on('connect_error', (error) => {
        console.error('❌ Socket connection error:', error);
        setIsConnected(false);
        
        reconnectAttempts.current += 1;
        if (reconnectAttempts.current >= maxReconnectAttempts) {
          toast.error('Unable to connect to server. Please refresh the page.');
        }
      });

      // Real-time event handlers
      newSocket.on('todo-created', (data) => {
        // Handle real-time todo creation
        window.dispatchEvent(new CustomEvent('todo-created', { detail: data }));
      });

      newSocket.on('todo-updated', (data) => {
        // Handle real-time todo updates
        window.dispatchEvent(new CustomEvent('todo-updated', { detail: data }));
      });

      newSocket.on('todo-deleted', (data) => {
        // Handle real-time todo deletion
        window.dispatchEvent(new CustomEvent('todo-deleted', { detail: data }));
      });

      newSocket.on('todo-completed', (data) => {
        // Handle real-time todo completion
        window.dispatchEvent(new CustomEvent('todo-completed', { detail: data }));
      });

      newSocket.on('note-created', (data) => {
        // Handle real-time note creation
        window.dispatchEvent(new CustomEvent('note-created', { detail: data }));
      });

      newSocket.on('note-updated', (data) => {
        // Handle real-time note updates
        window.dispatchEvent(new CustomEvent('note-updated', { detail: data }));
      });

      newSocket.on('note-deleted', (data) => {
        // Handle real-time note deletion
        window.dispatchEvent(new CustomEvent('note-deleted', { detail: data }));
      });

      newSocket.on('category-created', (data) => {
        // Handle real-time category creation
        window.dispatchEvent(new CustomEvent('category-created', { detail: data }));
      });

      newSocket.on('category-updated', (data) => {
        // Handle real-time category updates
        window.dispatchEvent(new CustomEvent('category-updated', { detail: data }));
      });

      newSocket.on('category-deleted', (data) => {
        // Handle real-time category deletion
        window.dispatchEvent(new CustomEvent('category-deleted', { detail: data }));
      });

      // Collaborative editing events
      newSocket.on('user-editing', (data) => {
        window.dispatchEvent(new CustomEvent('user-editing', { detail: data }));
      });

      newSocket.on('note-content-change', (data) => {
        window.dispatchEvent(new CustomEvent('note-content-change', { detail: data }));
      });

      newSocket.on('cursor-change', (data) => {
        window.dispatchEvent(new CustomEvent('cursor-change', { detail: data }));
      });

      newSocket.on('user-typing', (data) => {
        window.dispatchEvent(new CustomEvent('user-typing', { detail: data }));
      });

      newSocket.on('user-stopped-typing', (data) => {
        window.dispatchEvent(new CustomEvent('user-stopped-typing', { detail: data }));
      });

      // Presence events
      newSocket.on('user-presence-update', (data) => {
        setOnlineUsers(prev => {
          const newSet = new Set(prev);
          if (data.status === 'online') {
            newSet.add(data.userId);
          } else {
            newSet.delete(data.userId);
          }
          return newSet;
        });
      });

      return () => {
        newSocket.close();
      };
    }
  }, [user, token]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (socket) {
        socket.close();
      }
    };
  }, [socket]);

  // Socket utility functions
  const emit = (event, data) => {
    if (socket && isConnected) {
      socket.emit(event, data);
    }
  };

  const joinRoom = (roomId) => {
    if (socket && isConnected) {
      socket.emit('join-room', roomId);
    }
  };

  const leaveRoom = (roomId) => {
    if (socket && isConnected) {
      socket.emit('leave-room', roomId);
    }
  };

  // Todo-specific socket functions
  const emitTodoCreated = (todo) => {
    emit('todo-created', todo);
  };

  const emitTodoUpdated = (todo) => {
    emit('todo-updated', todo);
  };

  const emitTodoDeleted = (todoId) => {
    emit('todo-deleted', { id: todoId });
  };

  const emitTodoCompleted = (todo) => {
    emit('todo-completed', todo);
  };

  // Note-specific socket functions
  const emitNoteCreated = (note) => {
    emit('note-created', note);
  };

  const emitNoteUpdated = (note) => {
    emit('note-updated', note);
  };

  const emitNoteDeleted = (noteId) => {
    emit('note-deleted', { id: noteId });
  };

  // Collaborative editing functions
  const emitNoteEditing = (noteId, cursor) => {
    emit('note-editing', { noteId, cursor });
  };

  const emitNoteContentChange = (noteId, changes) => {
    emit('note-content-change', { noteId, changes });
  };

  const emitNoteCursorChange = (noteId, cursor) => {
    emit('note-cursor-change', { noteId, cursor });
  };

  const emitTypingStart = (noteId) => {
    emit('typing-start', { noteId });
  };

  const emitTypingStop = (noteId) => {
    emit('typing-stop', { noteId });
  };

  // Category-specific socket functions
  const emitCategoryCreated = (category) => {
    emit('category-created', category);
  };

  const emitCategoryUpdated = (category) => {
    emit('category-updated', category);
  };

  const emitCategoryDeleted = (categoryId) => {
    emit('category-deleted', { id: categoryId });
  };

  // Presence functions
  const updatePresence = (status) => {
    emit('user-presence', { status });
  };

  // Context value
  const value = {
    socket,
    isConnected,
    onlineUsers,
    emit,
    joinRoom,
    leaveRoom,
    // Todo functions
    emitTodoCreated,
    emitTodoUpdated,
    emitTodoDeleted,
    emitTodoCompleted,
    // Note functions
    emitNoteCreated,
    emitNoteUpdated,
    emitNoteDeleted,
    // Collaborative editing functions
    emitNoteEditing,
    emitNoteContentChange,
    emitNoteCursorChange,
    emitTypingStart,
    emitTypingStop,
    // Category functions
    emitCategoryCreated,
    emitCategoryUpdated,
    emitCategoryDeleted,
    // Presence functions
    updatePresence
  };

  return (
    <SocketContext.Provider value={value}>
      {children}
    </SocketContext.Provider>
  );
};

// Custom hook to use socket context
export const useSocket = () => {
  const context = useContext(SocketContext);
  if (!context) {
    throw new Error('useSocket must be used within a SocketProvider');
  }
  return context;
};

export default SocketContext;
