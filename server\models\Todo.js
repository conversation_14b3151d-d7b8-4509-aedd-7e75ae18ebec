const mongoose = require('mongoose');

const SubtaskSchema = new mongoose.Schema({
  title: {
    type: String,
    required: [true, 'Please add a subtask title'],
    trim: true,
    maxlength: [200, 'Subtask title cannot be more than 200 characters']
  },
  completed: {
    type: Boolean,
    default: false
  },
  order: {
    type: Number,
    default: 0
  }
}, {
  timestamps: true
});

const TodoSchema = new mongoose.Schema({
  title: {
    type: String,
    required: [true, 'Please add a todo title'],
    trim: true,
    maxlength: [200, 'Title cannot be more than 200 characters']
  },
  description: {
    type: String,
    maxlength: [1000, 'Description cannot be more than 1000 characters']
  },
  completed: {
    type: Boolean,
    default: false
  },
  priority: {
    type: String,
    enum: ['low', 'medium', 'high', 'urgent'],
    default: 'medium'
  },
  status: {
    type: String,
    enum: ['todo', 'in-progress', 'completed', 'cancelled'],
    default: 'todo'
  },
  dueDate: {
    type: Date,
    default: null
  },
  reminderDate: {
    type: Date,
    default: null
  },
  tags: [{
    type: String,
    trim: true,
    maxlength: [30, 'Tag cannot be more than 30 characters']
  }],
  subtasks: [SubtaskSchema],
  category: {
    type: mongoose.Schema.ObjectId,
    ref: 'Category',
    default: null
  },
  user: {
    type: mongoose.Schema.ObjectId,
    ref: 'User',
    required: true
  },
  assignedTo: [{
    type: mongoose.Schema.ObjectId,
    ref: 'User'
  }],
  attachments: [{
    filename: String,
    originalName: String,
    mimetype: String,
    size: Number,
    url: String,
    uploadedAt: {
      type: Date,
      default: Date.now
    }
  }],
  order: {
    type: Number,
    default: 0
  },
  estimatedTime: {
    type: Number, // in minutes
    default: null
  },
  actualTime: {
    type: Number, // in minutes
    default: null
  },
  isRecurring: {
    type: Boolean,
    default: false
  },
  recurringPattern: {
    type: {
      type: String,
      enum: ['daily', 'weekly', 'monthly', 'yearly'],
      default: 'daily'
    },
    interval: {
      type: Number,
      default: 1
    },
    endDate: Date,
    daysOfWeek: [Number], // 0-6, Sunday = 0
    dayOfMonth: Number, // 1-31
    monthOfYear: Number // 1-12
  },
  parentTodo: {
    type: mongoose.Schema.ObjectId,
    ref: 'Todo',
    default: null
  },
  completedAt: {
    type: Date,
    default: null
  },
  archivedAt: {
    type: Date,
    default: null
  },
  isArchived: {
    type: Boolean,
    default: false
  },
  isFavorite: {
    type: Boolean,
    default: false
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for subtask completion percentage
TodoSchema.virtual('subtaskProgress').get(function() {
  if (this.subtasks.length === 0) return 0;
  const completed = this.subtasks.filter(subtask => subtask.completed).length;
  return Math.round((completed / this.subtasks.length) * 100);
});

// Virtual for overdue status
TodoSchema.virtual('isOverdue').get(function() {
  if (!this.dueDate || this.completed) return false;
  return new Date() > this.dueDate;
});

// Virtual for days until due
TodoSchema.virtual('daysUntilDue').get(function() {
  if (!this.dueDate) return null;
  const today = new Date();
  const due = new Date(this.dueDate);
  const diffTime = due - today;
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
});

// Virtual for child todos (subtodos)
TodoSchema.virtual('childTodos', {
  ref: 'Todo',
  localField: '_id',
  foreignField: 'parentTodo',
  justOne: false
});

// Indexes for better query performance
TodoSchema.index({ user: 1, completed: 1 });
TodoSchema.index({ user: 1, dueDate: 1 });
TodoSchema.index({ user: 1, priority: 1 });
TodoSchema.index({ user: 1, category: 1 });
TodoSchema.index({ user: 1, tags: 1 });
TodoSchema.index({ user: 1, createdAt: -1 });

// Text index for search functionality
TodoSchema.index({
  title: 'text',
  description: 'text',
  tags: 'text'
});

// Pre-save middleware
TodoSchema.pre('save', function(next) {
  // Set completed date when marking as completed
  if (this.isModified('completed') && this.completed && !this.completedAt) {
    this.completedAt = new Date();
    this.status = 'completed';
  }
  
  // Clear completed date when marking as incomplete
  if (this.isModified('completed') && !this.completed && this.completedAt) {
    this.completedAt = null;
    this.status = 'todo';
  }

  // Update status based on completion
  if (this.isModified('completed')) {
    this.status = this.completed ? 'completed' : 'todo';
  }

  next();
});

// Static method to get user statistics
TodoSchema.statics.getUserStats = async function(userId) {
  const stats = await this.aggregate([
    { $match: { user: mongoose.Types.ObjectId(userId), isArchived: false } },
    {
      $group: {
        _id: null,
        total: { $sum: 1 },
        completed: { $sum: { $cond: ['$completed', 1, 0] } },
        pending: { $sum: { $cond: ['$completed', 0, 1] } },
        overdue: {
          $sum: {
            $cond: [
              {
                $and: [
                  { $not: '$completed' },
                  { $lt: ['$dueDate', new Date()] },
                  { $ne: ['$dueDate', null] }
                ]
              },
              1,
              0
            ]
          }
        },
        highPriority: { $sum: { $cond: [{ $eq: ['$priority', 'high'] }, 1, 0] } },
        urgent: { $sum: { $cond: [{ $eq: ['$priority', 'urgent'] }, 1, 0] } }
      }
    }
  ]);

  return stats[0] || {
    total: 0,
    completed: 0,
    pending: 0,
    overdue: 0,
    highPriority: 0,
    urgent: 0
  };
};

// Instance method to toggle completion
TodoSchema.methods.toggleCompletion = function() {
  this.completed = !this.completed;
  return this.save();
};

// Instance method to archive
TodoSchema.methods.archive = function() {
  this.isArchived = true;
  this.archivedAt = new Date();
  return this.save();
};

module.exports = mongoose.model('Todo', TodoSchema);
