{"name": "advanced-todo-notes-app", "version": "1.0.0", "description": "Advanced Todo and Notes application with MERN stack, Tailwind CSS, and Framer Motion", "main": "server/index.js", "scripts": {"dev": "concurrently \"npm run server\" \"npm run client\"", "server": "cd server && npm run dev", "client": "cd client && npm start", "build": "cd client && npm run build", "install-server": "cd server && npm install", "install-client": "cd client && npm install", "install-all": "npm run install-server && npm run install-client", "test": "cd server && npm test && cd ../client && npm test"}, "keywords": ["todo", "notes", "mern", "react", "nodejs", "mongodb", "tailwind", "framer-motion"], "author": "Advanced Developer", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}}