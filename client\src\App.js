import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Toaster } from 'react-hot-toast';
import { motion, AnimatePresence } from 'framer-motion';

// Context Providers
import { AuthProvider } from './contexts/AuthContext';
import { ThemeProvider } from './contexts/ThemeContext';
import { SocketProvider } from './contexts/SocketContext';

// Components
import Layout from './components/Layout/Layout';
import ProtectedRoute from './components/Auth/ProtectedRoute';
import LoadingSpinner from './components/UI/LoadingSpinner';

// Pages
import Login from './pages/Auth/Login';
import Register from './pages/Auth/Register';
import ForgotPassword from './pages/Auth/ForgotPassword';
import ResetPassword from './pages/Auth/ResetPassword';
import Dashboard from './pages/Dashboard/Dashboard';
import Todos from './pages/Todos/Todos';
import TodoDetail from './pages/Todos/TodoDetail';
import Notes from './pages/Notes/Notes';
import NoteDetail from './pages/Notes/NoteDetail';
import NoteEditor from './pages/Notes/NoteEditor';
import Categories from './pages/Categories/Categories';
import Settings from './pages/Settings/Settings';
import Profile from './pages/Profile/Profile';
import NotFound from './pages/NotFound/NotFound';

// Hooks
import { useAuth } from './hooks/useAuth';

// Styles
import './styles/globals.css';

function App() {
  const { user, loading, checkAuth } = useAuth();

  useEffect(() => {
    checkAuth();
  }, [checkAuth]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <ThemeProvider>
      <AuthProvider>
        <SocketProvider>
          <Router>
            <div className="App min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-200">
              <AnimatePresence mode="wait">
                <Routes>
                  {/* Public Routes */}
                  <Route 
                    path="/login" 
                    element={
                      user ? <Navigate to="/dashboard" replace /> : 
                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -20 }}
                        transition={{ duration: 0.3 }}
                      >
                        <Login />
                      </motion.div>
                    } 
                  />
                  <Route 
                    path="/register" 
                    element={
                      user ? <Navigate to="/dashboard" replace /> : 
                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -20 }}
                        transition={{ duration: 0.3 }}
                      >
                        <Register />
                      </motion.div>
                    } 
                  />
                  <Route 
                    path="/forgot-password" 
                    element={
                      user ? <Navigate to="/dashboard" replace /> : 
                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -20 }}
                        transition={{ duration: 0.3 }}
                      >
                        <ForgotPassword />
                      </motion.div>
                    } 
                  />
                  <Route 
                    path="/reset-password/:token" 
                    element={
                      user ? <Navigate to="/dashboard" replace /> : 
                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -20 }}
                        transition={{ duration: 0.3 }}
                      >
                        <ResetPassword />
                      </motion.div>
                    } 
                  />

                  {/* Protected Routes */}
                  <Route path="/" element={<ProtectedRoute><Layout /></ProtectedRoute>}>
                    <Route index element={<Navigate to="/dashboard" replace />} />
                    <Route path="dashboard" element={<Dashboard />} />
                    
                    {/* Todo Routes */}
                    <Route path="todos" element={<Todos />} />
                    <Route path="todos/:id" element={<TodoDetail />} />
                    
                    {/* Note Routes */}
                    <Route path="notes" element={<Notes />} />
                    <Route path="notes/new" element={<NoteEditor />} />
                    <Route path="notes/:id" element={<NoteDetail />} />
                    <Route path="notes/:id/edit" element={<NoteEditor />} />
                    
                    {/* Category Routes */}
                    <Route path="categories" element={<Categories />} />
                    
                    {/* User Routes */}
                    <Route path="profile" element={<Profile />} />
                    <Route path="settings" element={<Settings />} />
                  </Route>

                  {/* Catch all route */}
                  <Route path="*" element={<NotFound />} />
                </Routes>
              </AnimatePresence>

              {/* Global Toast Notifications */}
              <Toaster
                position="top-right"
                toastOptions={{
                  duration: 4000,
                  style: {
                    background: 'var(--toast-bg)',
                    color: 'var(--toast-color)',
                    border: '1px solid var(--toast-border)',
                  },
                  success: {
                    iconTheme: {
                      primary: '#10b981',
                      secondary: '#ffffff',
                    },
                  },
                  error: {
                    iconTheme: {
                      primary: '#ef4444',
                      secondary: '#ffffff',
                    },
                  },
                }}
              />
            </div>
          </Router>
        </SocketProvider>
      </AuthProvider>
    </ThemeProvider>
  );
}

export default App;
