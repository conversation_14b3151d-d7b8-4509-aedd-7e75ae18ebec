const jwt = require('jsonwebtoken');
const User = require('../models/User');

const socketHandler = (io) => {
  // Middleware to authenticate socket connections
  io.use(async (socket, next) => {
    try {
      const token = socket.handshake.auth.token;
      
      if (!token) {
        return next(new Error('Authentication error'));
      }

      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      const user = await User.findById(decoded.id).select('-password');
      
      if (!user) {
        return next(new Error('User not found'));
      }

      socket.userId = user._id.toString();
      socket.user = user;
      next();
    } catch (error) {
      next(new Error('Authentication error'));
    }
  });

  io.on('connection', (socket) => {
    console.log(`🔌 User ${socket.user.name} connected (${socket.userId})`);

    // Join user to their personal room
    socket.join(`user:${socket.userId}`);

    // Handle joining specific rooms (for shared notes/todos)
    socket.on('join-room', (roomId) => {
      socket.join(roomId);
      console.log(`📝 User ${socket.userId} joined room: ${roomId}`);
    });

    // Handle leaving specific rooms
    socket.on('leave-room', (roomId) => {
      socket.leave(roomId);
      console.log(`📝 User ${socket.userId} left room: ${roomId}`);
    });

    // Real-time todo updates
    socket.on('todo-created', (data) => {
      // Broadcast to user's room
      socket.to(`user:${socket.userId}`).emit('todo-created', data);
      
      // If todo is shared, broadcast to collaborators
      if (data.collaborators && data.collaborators.length > 0) {
        data.collaborators.forEach(collaboratorId => {
          socket.to(`user:${collaboratorId}`).emit('todo-created', data);
        });
      }
    });

    socket.on('todo-updated', (data) => {
      socket.to(`user:${socket.userId}`).emit('todo-updated', data);
      
      if (data.collaborators && data.collaborators.length > 0) {
        data.collaborators.forEach(collaboratorId => {
          socket.to(`user:${collaboratorId}`).emit('todo-updated', data);
        });
      }
    });

    socket.on('todo-deleted', (data) => {
      socket.to(`user:${socket.userId}`).emit('todo-deleted', data);
      
      if (data.collaborators && data.collaborators.length > 0) {
        data.collaborators.forEach(collaboratorId => {
          socket.to(`user:${collaboratorId}`).emit('todo-deleted', data);
        });
      }
    });

    socket.on('todo-completed', (data) => {
      socket.to(`user:${socket.userId}`).emit('todo-completed', data);
      
      if (data.collaborators && data.collaborators.length > 0) {
        data.collaborators.forEach(collaboratorId => {
          socket.to(`user:${collaboratorId}`).emit('todo-completed', data);
        });
      }
    });

    // Real-time note updates
    socket.on('note-created', (data) => {
      socket.to(`user:${socket.userId}`).emit('note-created', data);
      
      if (data.collaborators && data.collaborators.length > 0) {
        data.collaborators.forEach(collaborator => {
          socket.to(`user:${collaborator.user}`).emit('note-created', data);
        });
      }
    });

    socket.on('note-updated', (data) => {
      socket.to(`user:${socket.userId}`).emit('note-updated', data);
      
      if (data.collaborators && data.collaborators.length > 0) {
        data.collaborators.forEach(collaborator => {
          socket.to(`user:${collaborator.user}`).emit('note-updated', data);
        });
      }
    });

    socket.on('note-deleted', (data) => {
      socket.to(`user:${socket.userId}`).emit('note-deleted', data);
      
      if (data.collaborators && data.collaborators.length > 0) {
        data.collaborators.forEach(collaborator => {
          socket.to(`user:${collaborator.user}`).emit('note-deleted', data);
        });
      }
    });

    // Real-time collaborative editing
    socket.on('note-editing', (data) => {
      // Broadcast to note room that user is editing
      socket.to(`note:${data.noteId}`).emit('user-editing', {
        userId: socket.userId,
        userName: socket.user.name,
        noteId: data.noteId,
        cursor: data.cursor
      });
    });

    socket.on('note-content-change', (data) => {
      // Broadcast content changes to note room
      socket.to(`note:${data.noteId}`).emit('note-content-change', {
        noteId: data.noteId,
        changes: data.changes,
        userId: socket.userId,
        timestamp: Date.now()
      });
    });

    socket.on('note-cursor-change', (data) => {
      // Broadcast cursor position changes
      socket.to(`note:${data.noteId}`).emit('cursor-change', {
        userId: socket.userId,
        userName: socket.user.name,
        cursor: data.cursor
      });
    });

    // Category updates
    socket.on('category-created', (data) => {
      socket.to(`user:${socket.userId}`).emit('category-created', data);
    });

    socket.on('category-updated', (data) => {
      socket.to(`user:${socket.userId}`).emit('category-updated', data);
    });

    socket.on('category-deleted', (data) => {
      socket.to(`user:${socket.userId}`).emit('category-deleted', data);
    });

    // Typing indicators
    socket.on('typing-start', (data) => {
      socket.to(`note:${data.noteId}`).emit('user-typing', {
        userId: socket.userId,
        userName: socket.user.name,
        noteId: data.noteId
      });
    });

    socket.on('typing-stop', (data) => {
      socket.to(`note:${data.noteId}`).emit('user-stopped-typing', {
        userId: socket.userId,
        noteId: data.noteId
      });
    });

    // Presence updates
    socket.on('user-presence', (data) => {
      socket.broadcast.emit('user-presence-update', {
        userId: socket.userId,
        status: data.status,
        lastSeen: Date.now()
      });
    });

    // Handle disconnection
    socket.on('disconnect', (reason) => {
      console.log(`🔌 User ${socket.user.name} disconnected: ${reason}`);
      
      // Broadcast user offline status
      socket.broadcast.emit('user-presence-update', {
        userId: socket.userId,
        status: 'offline',
        lastSeen: Date.now()
      });
    });

    // Error handling
    socket.on('error', (error) => {
      console.error(`❌ Socket error for user ${socket.userId}:`, error);
    });
  });

  // Handle connection errors
  io.on('connect_error', (error) => {
    console.error('❌ Socket.io connection error:', error);
  });
};

module.exports = socketHandler;
