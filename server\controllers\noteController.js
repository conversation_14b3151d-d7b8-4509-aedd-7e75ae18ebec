const Note = require('../models/Note');

// @desc    Get all notes for user
// @route   GET /api/notes
// @access  Private
const getNotes = async (req, res, next) => {
  try {
    const notes = await Note.find({ 
      user: req.user.id,
      deletedAt: null 
    })
      .populate('category', 'name color icon')
      .sort({ createdAt: -1 });

    res.status(200).json({
      success: true,
      count: notes.length,
      data: notes
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get single note
// @route   GET /api/notes/:id
// @access  Private
const getNote = async (req, res, next) => {
  try {
    const note = await Note.findOne({
      _id: req.params.id,
      user: req.user.id,
      deletedAt: null
    }).populate('category', 'name color icon');

    if (!note) {
      return res.status(404).json({
        success: false,
        error: 'Note not found'
      });
    }

    res.status(200).json({
      success: true,
      data: note
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Create new note
// @route   POST /api/notes
// @access  Private
const createNote = async (req, res, next) => {
  try {
    req.body.user = req.user.id;
    req.body.metadata = {
      ...req.body.metadata,
      lastEditedBy: req.user.id
    };
    
    const note = await Note.create(req.body);
    
    // Emit socket event for real-time updates
    if (req.io) {
      req.io.to(`user:${req.user.id}`).emit('note-created', note);
    }

    res.status(201).json({
      success: true,
      data: note
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Update note
// @route   PUT /api/notes/:id
// @access  Private
const updateNote = async (req, res, next) => {
  try {
    const note = await Note.findOne({
      _id: req.params.id,
      user: req.user.id,
      deletedAt: null
    });

    if (!note) {
      return res.status(404).json({
        success: false,
        error: 'Note not found'
      });
    }

    // Create version before updating
    await note.createVersion(req.user.id, req.body.changeDescription || 'Updated note');

    // Update note
    Object.assign(note, req.body);
    note.metadata.lastEditedBy = req.user.id;
    await note.save();

    // Emit socket event for real-time updates
    if (req.io) {
      req.io.to(`user:${req.user.id}`).emit('note-updated', note);
    }

    res.status(200).json({
      success: true,
      data: note
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Delete note (soft delete)
// @route   DELETE /api/notes/:id
// @access  Private
const deleteNote = async (req, res, next) => {
  try {
    const note = await Note.findOne({
      _id: req.params.id,
      user: req.user.id,
      deletedAt: null
    });

    if (!note) {
      return res.status(404).json({
        success: false,
        error: 'Note not found'
      });
    }

    await note.softDelete();

    // Emit socket event for real-time updates
    if (req.io) {
      req.io.to(`user:${req.user.id}`).emit('note-deleted', { id: req.params.id });
    }

    res.status(200).json({
      success: true,
      data: {}
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Archive note
// @route   PATCH /api/notes/:id/archive
// @access  Private
const archiveNote = async (req, res, next) => {
  try {
    const note = await Note.findOne({
      _id: req.params.id,
      user: req.user.id,
      deletedAt: null
    });

    if (!note) {
      return res.status(404).json({
        success: false,
        error: 'Note not found'
      });
    }

    await note.archive();

    res.status(200).json({
      success: true,
      data: note
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Duplicate note
// @route   POST /api/notes/:id/duplicate
// @access  Private
const duplicateNote = async (req, res, next) => {
  try {
    const originalNote = await Note.findOne({
      _id: req.params.id,
      user: req.user.id,
      deletedAt: null
    });

    if (!originalNote) {
      return res.status(404).json({
        success: false,
        error: 'Note not found'
      });
    }

    const duplicatedNote = new Note({
      ...originalNote.toObject(),
      _id: undefined,
      title: `${originalNote.title} (Copy)`,
      isPublic: false,
      publicSlug: undefined,
      versions: [],
      version: 1,
      createdAt: undefined,
      updatedAt: undefined,
      metadata: {
        ...originalNote.metadata,
        lastEditedBy: req.user.id
      }
    });

    await duplicatedNote.save();

    res.status(201).json({
      success: true,
      data: duplicatedNote
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Share note
// @route   POST /api/notes/:id/share
// @access  Private
const shareNote = async (req, res, next) => {
  try {
    const note = await Note.findOne({
      _id: req.params.id,
      user: req.user.id,
      deletedAt: null
    });

    if (!note) {
      return res.status(404).json({
        success: false,
        error: 'Note not found'
      });
    }

    const { collaborators, isPublic } = req.body;

    if (collaborators) {
      note.collaborators = collaborators;
    }

    if (typeof isPublic === 'boolean') {
      note.isPublic = isPublic;
    }

    await note.save();

    res.status(200).json({
      success: true,
      data: note
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get shared note
// @route   GET /api/notes/shared/:shareId
// @access  Public
const getSharedNote = async (req, res, next) => {
  try {
    // Implementation for shared notes
    res.status(200).json({
      success: true,
      message: 'Shared note feature coming soon'
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get public note
// @route   GET /api/notes/public/:slug
// @access  Public
const getPublicNote = async (req, res, next) => {
  try {
    const note = await Note.findOne({
      publicSlug: req.params.slug,
      isPublic: true,
      deletedAt: null
    }).populate('user', 'name');

    if (!note) {
      return res.status(404).json({
        success: false,
        error: 'Note not found'
      });
    }

    res.status(200).json({
      success: true,
      data: note
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Create note version
// @route   POST /api/notes/:id/versions
// @access  Private
const createNoteVersion = async (req, res, next) => {
  try {
    const note = await Note.findOne({
      _id: req.params.id,
      user: req.user.id,
      deletedAt: null
    });

    if (!note) {
      return res.status(404).json({
        success: false,
        error: 'Note not found'
      });
    }

    await note.createVersion(req.user.id, req.body.changeDescription);

    res.status(201).json({
      success: true,
      data: note.versions[note.versions.length - 1]
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get note versions
// @route   GET /api/notes/:id/versions
// @access  Private
const getNoteVersions = async (req, res, next) => {
  try {
    const note = await Note.findOne({
      _id: req.params.id,
      user: req.user.id,
      deletedAt: null
    }).populate('versions.editedBy', 'name');

    if (!note) {
      return res.status(404).json({
        success: false,
        error: 'Note not found'
      });
    }

    res.status(200).json({
      success: true,
      data: note.versions
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Restore note version
// @route   POST /api/notes/:id/versions/:versionId/restore
// @access  Private
const restoreNoteVersion = async (req, res, next) => {
  try {
    const note = await Note.findOne({
      _id: req.params.id,
      user: req.user.id,
      deletedAt: null
    });

    if (!note) {
      return res.status(404).json({
        success: false,
        error: 'Note not found'
      });
    }

    const version = note.versions.id(req.params.versionId);
    if (!version) {
      return res.status(404).json({
        success: false,
        error: 'Version not found'
      });
    }

    // Create current version before restoring
    await note.createVersion(req.user.id, 'Before restoring version');

    // Restore version
    note.content = version.content;
    note.blocks = version.blocks;
    note.metadata.lastEditedBy = req.user.id;
    
    await note.save();

    res.status(200).json({
      success: true,
      data: note
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get note stats
// @route   GET /api/notes/stats
// @access  Private
const getNoteStats = async (req, res, next) => {
  try {
    const stats = await Note.getUserStats(req.user.id);

    res.status(200).json({
      success: true,
      data: stats
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Search notes
// @route   GET /api/notes/search
// @access  Private
const searchNotes = async (req, res, next) => {
  try {
    const { q } = req.query;
    
    const notes = await Note.find({
      user: req.user.id,
      deletedAt: null,
      $text: { $search: q }
    }).populate('category', 'name color icon');

    res.status(200).json({
      success: true,
      count: notes.length,
      data: notes
    });
  } catch (error) {
    next(error);
  }
};

module.exports = {
  getNotes,
  getNote,
  createNote,
  updateNote,
  deleteNote,
  archiveNote,
  duplicateNote,
  shareNote,
  getSharedNote,
  getPublicNote,
  createNoteVersion,
  getNoteVersions,
  restoreNoteVersion,
  getNoteStats,
  searchNotes
};
