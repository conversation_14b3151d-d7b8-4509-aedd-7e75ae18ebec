{"name": "todo-notes-client", "version": "1.0.0", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "react-router-dom": "^6.8.1", "axios": "^1.6.2", "framer-motion": "^10.16.16", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-beautiful-dnd": "^13.1.1", "react-quill": "^2.0.0", "quill": "^1.3.7", "date-fns": "^2.30.0", "socket.io-client": "^4.7.4", "react-intersection-observer": "^9.5.3", "react-virtual": "^2.10.4", "fuse.js": "^7.0.0", "zustand": "^4.4.7"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10"}, "proxy": "http://localhost:5000"}