import axios from 'axios';

// Create axios instance
const api = axios.create({
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:5000/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  register: (userData) => api.post('/auth/register', userData),
  login: (credentials) => api.post('/auth/login', credentials),
  logout: () => api.post('/auth/logout'),
  getMe: () => api.get('/auth/me'),
  updateDetails: (userData) => api.put('/auth/updatedetails', userData),
  updatePassword: (passwordData) => api.put('/auth/updatepassword', passwordData),
  updatePreferences: (preferences) => api.put('/auth/preferences', preferences),
  forgotPassword: (email) => api.post('/auth/forgotpassword', email),
  resetPassword: (token, passwordData) => api.put(`/auth/resetpassword/${token}`, passwordData),
};

// Users API
export const usersAPI = {
  getUsers: (params) => api.get('/users', { params }),
  getUser: (id) => api.get(`/users/${id}`),
  updateUser: (id, userData) => api.put(`/users/${id}`, userData),
  deleteUser: (id) => api.delete(`/users/${id}`),
  getUserStats: () => api.get('/users/stats'),
};

// Todos API
export const todosAPI = {
  getTodos: (params) => api.get('/todos', { params }),
  getTodo: (id) => api.get(`/todos/${id}`),
  createTodo: (todoData) => api.post('/todos', todoData),
  updateTodo: (id, todoData) => api.put(`/todos/${id}`, todoData),
  deleteTodo: (id) => api.delete(`/todos/${id}`),
  toggleTodo: (id) => api.patch(`/todos/${id}/toggle`),
  archiveTodo: (id) => api.patch(`/todos/${id}/archive`),
  duplicateTodo: (id) => api.post(`/todos/${id}/duplicate`),
  bulkUpdateTodos: (updates) => api.put('/todos/bulk', updates),
  getTodoStats: () => api.get('/todos/stats'),
  searchTodos: (query, params) => api.get('/todos/search', { params: { q: query, ...params } }),
  getUpcomingTodos: (params) => api.get('/todos/upcoming', { params }),
  getOverdueTodos: (params) => api.get('/todos/overdue', { params }),
};

// Notes API
export const notesAPI = {
  getNotes: (params) => api.get('/notes', { params }),
  getNote: (id) => api.get(`/notes/${id}`),
  createNote: (noteData) => api.post('/notes', noteData),
  updateNote: (id, noteData) => api.put(`/notes/${id}`, noteData),
  deleteNote: (id) => api.delete(`/notes/${id}`),
  archiveNote: (id) => api.patch(`/notes/${id}/archive`),
  duplicateNote: (id) => api.post(`/notes/${id}/duplicate`),
  shareNote: (id, shareData) => api.post(`/notes/${id}/share`, shareData),
  getSharedNote: (shareId) => api.get(`/notes/shared/${shareId}`),
  getPublicNote: (slug) => api.get(`/notes/public/${slug}`),
  createNoteVersion: (id, versionData) => api.post(`/notes/${id}/versions`, versionData),
  getNoteVersions: (id) => api.get(`/notes/${id}/versions`),
  restoreNoteVersion: (id, versionId) => api.post(`/notes/${id}/versions/${versionId}/restore`),
  getNoteStats: () => api.get('/notes/stats'),
  searchNotes: (query, params) => api.get('/notes/search', { params: { q: query, ...params } }),
};

// Categories API
export const categoriesAPI = {
  getCategories: (params) => api.get('/categories', { params }),
  getCategory: (id) => api.get(`/categories/${id}`),
  createCategory: (categoryData) => api.post('/categories', categoryData),
  updateCategory: (id, categoryData) => api.put(`/categories/${id}`, categoryData),
  deleteCategory: (id) => api.delete(`/categories/${id}`),
  reorderCategories: (orderData) => api.put('/categories/reorder', orderData),
};

// File upload API
export const uploadAPI = {
  uploadFile: (file, onProgress) => {
    const formData = new FormData();
    formData.append('file', file);
    
    return api.post('/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress) {
          const percentCompleted = Math.round(
            (progressEvent.loaded * 100) / progressEvent.total
          );
          onProgress(percentCompleted);
        }
      },
    });
  },
  deleteFile: (fileId) => api.delete(`/upload/${fileId}`),
};

// Search API
export const searchAPI = {
  globalSearch: (query, params) => api.get('/search', { params: { q: query, ...params } }),
  searchSuggestions: (query) => api.get('/search/suggestions', { params: { q: query } }),
};

// Analytics API
export const analyticsAPI = {
  getDashboardStats: () => api.get('/analytics/dashboard'),
  getProductivityStats: (period) => api.get('/analytics/productivity', { params: { period } }),
  getActivityLog: (params) => api.get('/analytics/activity', { params }),
};

export default api;
