const express = require('express');
const { body } = require('express-validator');
const { protect } = require('../middleware/auth');
const {
  getNotes,
  getNote,
  createNote,
  updateNote,
  deleteNote,
  searchNotes,
  getNoteStats,
  archiveNote,
  duplicateNote,
  shareNote,
  getSharedNote,
  createNoteVersion,
  getNoteVersions,
  restoreNoteVersion,
  getPublicNote
} = require('../controllers/noteController');

const router = express.Router();

// Public routes
router.route('/public/:slug')
  .get(getPublicNote);

// Protected routes
router.use(protect);

// Validation rules
const noteValidation = [
  body('title')
    .trim()
    .isLength({ min: 1, max: 200 })
    .withMessage('Title must be between 1 and 200 characters'),
  body('content')
    .optional()
    .isLength({ max: 50000 })
    .withMessage('Content cannot be more than 50,000 characters'),
  body('tags')
    .optional()
    .isArray()
    .withMessage('Tags must be an array'),
  body('tags.*')
    .optional()
    .isLength({ max: 30 })
    .withMessage('Each tag cannot be more than 30 characters')
];

// Routes
router.route('/')
  .get(getNotes)
  .post(noteValidation, createNote);

router.route('/stats')
  .get(getNoteStats);

router.route('/search')
  .get(searchNotes);

router.route('/:id')
  .get(getNote)
  .put(noteValidation, updateNote)
  .delete(deleteNote);

router.route('/:id/archive')
  .patch(archiveNote);

router.route('/:id/duplicate')
  .post(duplicateNote);

router.route('/:id/share')
  .post(shareNote);

router.route('/:id/versions')
  .get(getNoteVersions)
  .post(createNoteVersion);

router.route('/:id/versions/:versionId/restore')
  .post(restoreNoteVersion);

router.route('/shared/:shareId')
  .get(getSharedNote);

module.exports = router;
